* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
}

#container {
    display: flex;
    height: 100vh;
}

#controls-panel {
    width: 300px;
    background: #2a2a2a;
    padding: 20px;
    border-right: 1px solid #444;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3);
}

#controls-panel h2 {
    margin-bottom: 20px;
    color: #4CAF50;
    text-align: center;
    border-bottom: 2px solid #4CAF50;
    padding-bottom: 10px;
}

.control-group {
    margin-bottom: 20px;
}

.control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #cccccc;
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #444;
    outline: none;
    -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #4CAF50;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.control-group span {
    display: inline-block;
    margin-top: 5px;
    padding: 4px 8px;
    background: #333;
    border-radius: 4px;
    font-weight: bold;
    color: #4CAF50;
    min-width: 60px;
    text-align: center;
}

#regenerate {
    width: 100%;
    padding: 12px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
    margin-top: 10px;
}

#regenerate:hover {
    background: #45a049;
}

#regenerate:active {
    transform: translateY(1px);
}

.info-panel {
    margin-top: 30px;
    padding: 15px;
    background: #333;
    border-radius: 8px;
    border-left: 4px solid #4CAF50;
}

.info-panel h3 {
    margin-bottom: 15px;
    color: #4CAF50;
}

.info-panel p {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
}

.info-panel span {
    color: #4CAF50;
    font-weight: bold;
}

#renderCanvas-container {
    flex: 1;
    position: relative;
}

#renderCanvas {
    width: 100%;
    height: 100%;
    display: block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Scrollbar styling */
#controls-panel::-webkit-scrollbar {
    width: 8px;
}

#controls-panel::-webkit-scrollbar-track {
    background: #1a1a1a;
}

#controls-panel::-webkit-scrollbar-thumb {
    background: #4CAF50;
    border-radius: 4px;
}

#controls-panel::-webkit-scrollbar-thumb:hover {
    background: #45a049;
}
