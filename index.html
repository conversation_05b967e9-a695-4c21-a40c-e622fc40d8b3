<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parametric Stringer Generator</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js"></script>
    <script src="https://cdn.babylonjs.com/materialsLibrary/babylonjs.materials.min.js"></script>
    <script src="https://cdn.babylonjs.com/earcut.min.js"></script>
</head>
<body>
    <div id="container">
        <div id="controls-panel">
            <h2>Stringer Parameters</h2>

            <div class="control-group">
                <label for="unitRun">Unit Run (mm):</label>
                <input type="range" id="unitRun" min="200" max="400" value="280" step="10">
                <span id="unitRunValue">280</span>
            </div>

            <div class="control-group">
                <label for="unitRise">Unit Rise (mm):</label>
                <input type="range" id="unitRise" min="150" max="250" value="180" step="10">
                <span id="unitRiseValue">180</span>
            </div>

            <div class="control-group">
                <label for="thickness">Material Thickness (mm):</label>
                <input type="range" id="thickness" min="20" max="60" value="40" step="5">
                <span id="thicknessValue">40</span>
            </div>

            <div class="control-group">
                <label for="numSteps">Number of Steps:</label>
                <input type="range" id="numSteps" min="3" max="15" value="8" step="1">
                <span id="numStepsValue">8</span>
            </div>

            <div class="control-group">
                <label for="stringerLength">Stringer Length (mm):</label>
                <input type="range" id="stringerLength" min="2000" max="5000" value="3000" step="100">
                <span id="stringerLengthValue">3000</span>
            </div>

            <button id="regenerate">Regenerate Stringer</button>

            <div class="info-panel">
                <h3>Calculated Values</h3>
                <div id="calculations">
                    <p>Total Rise: <span id="totalRise">0</span> mm</p>
                    <p>Total Run: <span id="totalRun">0</span> mm</p>
                    <p>Angle: <span id="angle">0</span>°</p>
                </div>
            </div>
        </div>

        <div id="renderCanvas-container">
            <canvas id="renderCanvas"></canvas>
        </div>
    </div>

    <script src="stringer.js"></script>
</body>
</html>
