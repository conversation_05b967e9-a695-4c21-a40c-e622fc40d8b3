class StringerGenerator {
    constructor() {
        this.canvas = document.getElementById('renderCanvas');
        this.engine = new BABYLON.Engine(this.canvas, true);
        this.scene = null;
        this.camera = null;
        this.stringerMesh = null;

        this.parameters = {
            unitRun: 280,
            unitRise: 180,
            thickness: 100,
            numSteps: 8,
            stringerLength: 3000
        };

        this.init();
        this.setupEventListeners();
        this.generateStringer();
    }

    init() {
        // Create scene
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = new BABYLON.Color3(0.1, 0.1, 0.15);

        // Create camera
        this.camera = new BABYLON.ArcRotateCamera(
            "camera",
            -Math.PI / 2,
            Math.PI / 3,
            2000,
            BABYLON.Vector3.Zero(),
            this.scene
        );
        this.camera.attachControl(this.canvas, true);
        this.camera.setTarget(BABYLON.Vector3.Zero());

        // Create lights
        const hemisphericLight = new BABYLON.HemisphericLight(
            "hemisphericLight",
            new BABYLON.Vector3(0, 1, 0),
            this.scene
        );
        hemisphericLight.intensity = 0.7;

        const directionalLight = new BABYLON.DirectionalLight(
            "directionalLight",
            new BABYLON.Vector3(-1, -1, -1),
            this.scene
        );
        directionalLight.intensity = 0.5;

        // Start render loop
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.engine.resize();
        });
    }

    setupEventListeners() {
        // Parameter controls
        const controls = ['unitRun', 'unitRise', 'thickness', 'numSteps', 'stringerLength'];

        controls.forEach(control => {
            const slider = document.getElementById(control);
            const valueDisplay = document.getElementById(control + 'Value');

            slider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.parameters[control] = value;
                valueDisplay.textContent = value;
                this.updateCalculations();
                this.generateStringer();
            });
        });

        // Regenerate button
        document.getElementById('regenerate').addEventListener('click', () => {
            this.generateStringer();
        });

        // Initialize displays
        controls.forEach(control => {
            const valueDisplay = document.getElementById(control + 'Value');
            valueDisplay.textContent = this.parameters[control];
        });

        this.updateCalculations();
    }

    updateCalculations() {
        const totalRise = this.parameters.unitRise * this.parameters.numSteps;
        const totalRun = this.parameters.unitRun * this.parameters.numSteps;
        const angle = Math.atan(totalRise / totalRun) * (180 / Math.PI);

        document.getElementById('totalRise').textContent = totalRise;
        document.getElementById('totalRun').textContent = totalRun;
        document.getElementById('angle').textContent = angle.toFixed(1);
    }

    createStringerProfile() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;

        // Create the stringer profile with linear bottom edge and step cutouts
        const points = [];

        // Calculate total dimensions
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Start at bottom left (0,0) - LINEAR BOTTOM EDGE
        points.push(new BABYLON.Vector3(0, 0, 0));

        // Linear bottom edge to the right - CRITICAL REQUIREMENT
        points.push(new BABYLON.Vector3(totalRun, 0, 0));

        // Go up to create the angled top edge
        points.push(new BABYLON.Vector3(totalRun, totalRise + thickness, 0));

        // Create the stepped profile from right to left
        for (let i = numSteps - 1; i >= 0; i--) {
            const stepX = i * unitRun;
            const stepY = i * unitRise;

            // Horizontal line at top of step
            if (i < numSteps - 1) {
                points.push(new BABYLON.Vector3(stepX + unitRun, stepY + unitRise + thickness, 0));
            }

            // Vertical cut for riser
            points.push(new BABYLON.Vector3(stepX + unitRun, stepY + thickness, 0));

            // Horizontal cut for tread
            points.push(new BABYLON.Vector3(stepX, stepY + thickness, 0));

            // Vertical line down if not the last step
            if (i > 0) {
                points.push(new BABYLON.Vector3(stepX, (i-1) * unitRise + thickness, 0));
            }
        }

        // Close the profile - left edge back to start
        points.push(new BABYLON.Vector3(0, thickness, 0));
        points.push(new BABYLON.Vector3(0, 0, 0));

        console.log("Profile created with", points.length, "points");
        console.log("First point:", points[0]);
        console.log("Last point:", points[points.length-1]);

        return points;
    }

    generateStringer() {
        console.log("=== GENERATING NEW STRINGER ===");

        // Remove ALL existing meshes
        this.scene.meshes.forEach(mesh => {
            if (mesh.name !== 'camera' && mesh.name !== 'ground') {
                console.log("Disposing mesh:", mesh.name);
                mesh.dispose();
            }
        });

        // FORCE the correct method
        this.createStringerFromScratch();
    }

    createStringerFromScratch() {
        const { unitRun, unitRise, numSteps, thickness } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        console.log("Creating ONE PIECE stringer with rectangular cutouts - EXACTLY like your drawing");

        // Step 1: Create the triangular block (like in your drawing)
        const stringerHeight = totalRise + thickness;

        // Create the profile points for a TRIANGULAR block with LINEAR BOTTOM
        const profilePoints = [
            new BABYLON.Vector2(0, 0),                    // Bottom left
            new BABYLON.Vector2(totalRun, 0),             // Bottom right (COMPLETELY LINEAR BOTTOM)
            new BABYLON.Vector2(totalRun, stringerHeight), // Top right (full height)
            new BABYLON.Vector2(0, stringerHeight),       // Top left (full height) - THIS IS THE KEY!
            new BABYLON.Vector2(0, 0)                     // Close the shape
        ];

        console.log("Creating triangular block with profile points:", profilePoints.length);

        try {
            // Create the main triangular block using ExtrudePolygon
            this.stringerMesh = BABYLON.MeshBuilder.ExtrudePolygon("triangularStringer", {
                shape: profilePoints,
                depth: thickness,
                sideOrientation: BABYLON.Mesh.DOUBLESIDE
            }, this.scene);

            console.log("Triangular block created successfully!");

            // Step 2: Cut out the rectangular step cutouts (CUT CUT WASTE from your drawing)
            if (typeof BABYLON.CSG !== 'undefined') {
                console.log("Starting CSG cutouts...");

                let stringerCSG = BABYLON.CSG.FromMesh(this.stringerMesh);

                for (let i = 0; i < numSteps; i++) {
                    // Create rectangular cutout for each step
                    const cutout = BABYLON.MeshBuilder.CreateBox(`cutout_${i}`, {
                        width: unitRun,
                        height: unitRise,
                        depth: thickness * 2 // Make it thicker to ensure clean cut
                    }, this.scene);

                    // Position the cutout exactly where the step should be
                    cutout.position.x = i * unitRun + unitRun / 2;
                    cutout.position.y = i * unitRise + thickness + unitRise / 2;
                    cutout.position.z = 0;

                    // Subtract the cutout from the stringer
                    const cutoutCSG = BABYLON.CSG.FromMesh(cutout);
                    stringerCSG = stringerCSG.subtract(cutoutCSG);

                    cutout.dispose();
                    console.log(`Cutout ${i + 1} completed`);
                }

                // Replace the original mesh with the cut version
                this.stringerMesh.dispose();
                this.stringerMesh = stringerCSG.toMesh("cutStringer", null, this.scene);

                console.log("All cutouts completed - ONE PIECE stringer created!");

            } else {
                console.log("CSG not available - showing triangular block only");
            }

        } catch (error) {
            console.error("ExtrudePolygon failed:", error);

            // Fallback: simple box
            this.stringerMesh = BABYLON.MeshBuilder.CreateBox("fallbackStringer", {
                width: totalRun,
                height: stringerHeight,
                depth: thickness
            }, this.scene);
        }

        // Apply wood material
        const material = new BABYLON.StandardMaterial("wood", this.scene);
        material.diffuseColor = new BABYLON.Color3(0.8, 0.6, 0.4);
        this.stringerMesh.material = material;

        // Position the stringer
        this.stringerMesh.position.set(-totalRun/2, 0, 0);

        // Update camera
        this.camera.setTarget(new BABYLON.Vector3(0, totalRise/2, 0));
        this.camera.radius = Math.max(totalRun, totalRise) * 1.5;

        console.log("ONE PIECE stringer completed!");
    }

    createRealStringer() {
        const { unitRun, unitRise, numSteps, thickness } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        console.log("Creating REAL stringer with step cutouts");
        console.log("CSG available:", typeof BABYLON.CSG !== 'undefined');

        // Check if CSG is available
        if (typeof BABYLON.CSG === 'undefined') {
            console.warn("CSG not available, creating visual representation");
            this.createVisualStringer();
            return;
        }

        // Step 1: Create the main triangular body
        const stringerHeight = totalRise + thickness;
        const stringerWidth = totalRun;

        // Create main rectangular block
        const mainBlock = BABYLON.MeshBuilder.CreateBox("mainBlock", {
            width: stringerWidth,
            height: stringerHeight,
            depth: thickness
        }, this.scene);

        // Position it correctly
        mainBlock.position.x = stringerWidth / 2;
        mainBlock.position.y = stringerHeight / 2;
        mainBlock.position.z = 0;

        // Step 2: Create step cutouts and subtract them
        let result = mainBlock;

        for (let i = 0; i < numSteps; i++) {
            // Create cutout box for each step
            const cutout = BABYLON.MeshBuilder.CreateBox(`cutout${i}`, {
                width: unitRun + 1, // Slightly larger for clean cut
                height: unitRise + 1,
                depth: thickness + 2
            }, this.scene);

            // Position cutout at the correct step location
            cutout.position.x = i * unitRun + (unitRun / 2);
            cutout.position.y = i * unitRise + (unitRise / 2) + thickness;
            cutout.position.z = 0;

            // Perform boolean subtraction using official Babylon.js CSG
            try {
                const resultCSG = BABYLON.CSG.FromMesh(result);
                const cutoutCSG = BABYLON.CSG.FromMesh(cutout);
                const newResult = resultCSG.subtract(cutoutCSG).toMesh(`stringer_step_${i}`, result.material, this.scene);

                // Clean up old meshes
                if (result !== mainBlock) {
                    result.dispose();
                }
                cutout.dispose();
                result = newResult;

                console.log(`Step ${i + 1} cutout completed`);
            } catch (error) {
                console.error(`CSG operation failed for step ${i}:`, error);
                cutout.dispose();
            }
        }

        this.stringerMesh = result;
        this.applyStringerMaterial();
        this.positionStringer();

        console.log("Real stringer created with", numSteps, "step cutouts");
    }

    createVisualStringer() {
        const { unitRun, unitRise, numSteps, thickness } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        console.log("Creating REAL stringer like in your drawings");

        // Clear any existing meshes first
        this.scene.meshes.forEach(mesh => {
            if (mesh.name.includes('mainBlock') || mesh.name.includes('segment') || mesh.name.includes('vertical')) {
                mesh.dispose();
            }
        });

        // Create the exact stringer profile from your drawings
        const stringerProfile = [];

        // Start at bottom left (0, 0) - LINEAR BOTTOM EDGE
        stringerProfile.push(new BABYLON.Vector3(0, 0, 0));

        // Linear bottom edge to the right
        stringerProfile.push(new BABYLON.Vector3(totalRun, 0, 0));

        // Go up at the right end
        stringerProfile.push(new BABYLON.Vector3(totalRun, totalRise + thickness, 0));

        // Create the stepped top profile (from right to left)
        for (let i = numSteps - 1; i >= 0; i--) {
            const stepX = i * unitRun;
            const stepY = i * unitRise + thickness;

            // Top of step
            stringerProfile.push(new BABYLON.Vector3(stepX + unitRun, stepY + unitRise, 0));
            // Vertical drop
            stringerProfile.push(new BABYLON.Vector3(stepX + unitRun, stepY, 0));
            // Horizontal cut
            stringerProfile.push(new BABYLON.Vector3(stepX, stepY, 0));
        }

        // Close the profile back to start
        stringerProfile.push(new BABYLON.Vector3(0, thickness, 0));
        stringerProfile.push(new BABYLON.Vector3(0, 0, 0));

        // Convert to 2D shape for extrusion
        const shape2D = stringerProfile.map(p => new BABYLON.Vector2(p.x, p.y));

        console.log("Profile points:", shape2D.length);

        try {
            // Try ExtrudePolygon first
            this.stringerMesh = BABYLON.MeshBuilder.ExtrudePolygon("realStringer", {
                shape: shape2D,
                depth: thickness,
                sideOrientation: BABYLON.Mesh.DOUBLESIDE
            }, this.scene);

            console.log("ExtrudePolygon successful!");

        } catch (error) {
            console.log("ExtrudePolygon failed, using simple extrusion");

            // Fallback: use simple box with correct proportions
            this.stringerMesh = BABYLON.MeshBuilder.CreateBox("simpleStringer", {
                width: totalRun,
                height: totalRise + thickness,
                depth: thickness
            }, this.scene);

            // Make it red to show it's a fallback
            const fallbackMaterial = new BABYLON.StandardMaterial("fallback", this.scene);
            fallbackMaterial.diffuseColor = new BABYLON.Color3(1, 0, 0);
            this.stringerMesh.material = fallbackMaterial;
        }

        this.applyStringerMaterial();
        this.positionStringer();

        console.log("Real stringer created");
    }

    createFallbackStringer() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        console.log("Creating fallback stringer - ExtrudePolygon failed");

        // Create a simple box as fallback with correct dimensions
        this.stringerMesh = BABYLON.MeshBuilder.CreateBox("stringerFallback", {
            width: totalRun,
            height: totalRise + thickness,
            depth: thickness
        }, this.scene);

        // Apply RED material to indicate fallback
        const fallbackMaterial = new BABYLON.StandardMaterial("fallbackMaterial", this.scene);
        fallbackMaterial.diffuseColor = new BABYLON.Color3(1, 0, 0); // RED to indicate error
        this.stringerMesh.material = fallbackMaterial;

        // Position the stringer
        this.stringerMesh.position.x = 0;
        this.stringerMesh.position.y = totalRise / 2;
        this.stringerMesh.position.z = 0;

        console.log("Fallback stringer created with dimensions:", totalRun, "x", totalRise + thickness, "x", thickness);

        this.updateCamera();
    }

    applyStringerMaterial() {
        const material = new BABYLON.StandardMaterial("stringerMaterial", this.scene);
        material.diffuseColor = new BABYLON.Color3(0.8, 0.6, 0.4); // Wood color
        material.specularColor = new BABYLON.Color3(0.2, 0.2, 0.2);
        material.wireframe = false; // Set to true to see wireframe
        this.stringerMesh.material = material;

        // Also create wireframe version for debugging
        const wireframeMaterial = new BABYLON.StandardMaterial("wireframeMaterial", this.scene);
        wireframeMaterial.wireframe = true;
        wireframeMaterial.diffuseColor = new BABYLON.Color3(0, 1, 0); // Green wireframe

        // Uncomment next line to see wireframe instead
        // this.stringerMesh.material = wireframeMaterial;
    }

    positionStringer() {
        const { unitRun, unitRise, numSteps, stringerLength } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Center the stringer properly
        this.stringerMesh.position.x = -totalRun / 2;
        this.stringerMesh.position.y = 0;
        this.stringerMesh.position.z = 0;

        // Rotate to see it better (optional)
        this.stringerMesh.rotation.x = 0;
        this.stringerMesh.rotation.y = 0;
        this.stringerMesh.rotation.z = 0;

        console.log("Stringer positioned at:", this.stringerMesh.position);
        console.log("Stringer dimensions - totalRun:", totalRun, "totalRise:", totalRise);

        this.updateCamera();
    }

    updateCamera() {
        const { unitRun, unitRise, numSteps, stringerLength } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Position camera to see the stringer well
        this.camera.setTarget(new BABYLON.Vector3(0, totalRise / 2, 0));
        this.camera.radius = Math.max(totalRun, totalRise) * 2;
        this.camera.alpha = -Math.PI / 4; // Angle around Y axis
        this.camera.beta = Math.PI / 3;   // Angle from Y axis

        console.log("Camera updated - target:", this.camera.target, "radius:", this.camera.radius);
    }
}

// Initialize the application when page loads
window.addEventListener('DOMContentLoaded', () => {
    new StringerGenerator();
});
