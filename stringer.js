class StringerGenerator {
    constructor() {
        this.canvas = document.getElementById('renderCanvas');
        this.engine = new BABYLON.Engine(this.canvas, true);
        this.scene = null;
        this.camera = null;
        this.stringerMesh = null;

        this.parameters = {
            unitRun: 280,
            unitRise: 180,
            thickness: 40,
            numSteps: 8,
            stringerLength: 3000
        };

        this.init();
        this.setupEventListeners();
        this.generateStringer();
    }

    init() {
        // Create scene
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = new BABYLON.Color3(0.1, 0.1, 0.15);

        // Create camera
        this.camera = new BABYLON.ArcRotateCamera(
            "camera",
            -Math.PI / 2,
            Math.PI / 3,
            2000,
            BABYLON.Vector3.Zero(),
            this.scene
        );
        this.camera.attachControl(this.canvas, true);
        this.camera.setTarget(BABYLON.Vector3.Zero());

        // Create lights
        const hemisphericLight = new BABYLON.HemisphericLight(
            "hemisphericLight",
            new BABYLON.Vector3(0, 1, 0),
            this.scene
        );
        hemisphericLight.intensity = 0.7;

        const directionalLight = new BABYLON.DirectionalLight(
            "directionalLight",
            new BABYLON.Vector3(-1, -1, -1),
            this.scene
        );
        directionalLight.intensity = 0.5;

        // Start render loop
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.engine.resize();
        });
    }

    setupEventListeners() {
        // Parameter controls
        const controls = ['unitRun', 'unitRise', 'thickness', 'numSteps', 'stringerLength'];

        controls.forEach(control => {
            const slider = document.getElementById(control);
            const valueDisplay = document.getElementById(control + 'Value');

            slider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.parameters[control] = value;
                valueDisplay.textContent = value;
                this.updateCalculations();
                this.generateStringer();
            });
        });

        // Regenerate button
        document.getElementById('regenerate').addEventListener('click', () => {
            this.generateStringer();
        });

        // Initialize displays
        controls.forEach(control => {
            const valueDisplay = document.getElementById(control + 'Value');
            valueDisplay.textContent = this.parameters[control];
        });

        this.updateCalculations();
    }

    updateCalculations() {
        const totalRise = this.parameters.unitRise * this.parameters.numSteps;
        const totalRun = this.parameters.unitRun * this.parameters.numSteps;
        const angle = Math.atan(totalRise / totalRun) * (180 / Math.PI);

        document.getElementById('totalRise').textContent = totalRise;
        document.getElementById('totalRun').textContent = totalRun;
        document.getElementById('angle').textContent = angle.toFixed(1);
    }

    createStringerProfile() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;

        // Create the stringer profile with linear bottom edge and step cutouts
        const points = [];

        // Calculate total dimensions
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Start at bottom left (0,0) - LINEAR BOTTOM EDGE
        points.push(new BABYLON.Vector3(0, 0, 0));

        // Linear bottom edge to the right - CRITICAL REQUIREMENT
        points.push(new BABYLON.Vector3(totalRun, 0, 0));

        // Go up to create the angled top edge
        points.push(new BABYLON.Vector3(totalRun, totalRise + thickness, 0));

        // Create the stepped profile from right to left
        for (let i = numSteps - 1; i >= 0; i--) {
            const stepX = i * unitRun;
            const stepY = i * unitRise;

            // Horizontal line at top of step
            if (i < numSteps - 1) {
                points.push(new BABYLON.Vector3(stepX + unitRun, stepY + unitRise + thickness, 0));
            }

            // Vertical cut for riser
            points.push(new BABYLON.Vector3(stepX + unitRun, stepY + thickness, 0));

            // Horizontal cut for tread
            points.push(new BABYLON.Vector3(stepX, stepY + thickness, 0));

            // Vertical line down if not the last step
            if (i > 0) {
                points.push(new BABYLON.Vector3(stepX, (i-1) * unitRise + thickness, 0));
            }
        }

        // Close the profile - left edge back to start
        points.push(new BABYLON.Vector3(0, thickness, 0));
        points.push(new BABYLON.Vector3(0, 0, 0));

        console.log("Profile created with", points.length, "points");
        console.log("First point:", points[0]);
        console.log("Last point:", points[points.length-1]);

        return points;
    }

    generateStringer() {
        // Remove existing stringer
        if (this.stringerMesh) {
            this.stringerMesh.dispose();
        }

        try {
            this.createParametricStringer();
        } catch (error) {
            console.error("Error creating parametric stringer:", error);
            this.createFallbackStringer();
        }
    }

    createParametricStringer() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;

        // Create the stringer profile with linear bottom edge and step cutouts
        const profile = this.createStringerProfile();

        // Convert to Vector2 array for ExtrudePolygon
        const shape = profile.map(point => new BABYLON.Vector2(point.x, point.y));

        console.log("Creating stringer with profile points:", shape.length);

        // Use ExtrudePolygon according to official docs
        this.stringerMesh = BABYLON.MeshBuilder.ExtrudePolygon("stringer", {
            shape: shape,
            depth: thickness,
            sideOrientation: BABYLON.Mesh.DOUBLESIDE
        }, this.scene);

        // Apply material
        this.applyStringerMaterial();

        // Position and orient the stringer
        this.positionStringer();

        console.log("Parametric stringer created successfully");
    }

    createFallbackStringer() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        console.log("Creating fallback stringer - ExtrudePolygon failed");

        // Create a simple box as fallback with correct dimensions
        this.stringerMesh = BABYLON.MeshBuilder.CreateBox("stringerFallback", {
            width: totalRun,
            height: totalRise + thickness,
            depth: thickness
        }, this.scene);

        // Apply RED material to indicate fallback
        const fallbackMaterial = new BABYLON.StandardMaterial("fallbackMaterial", this.scene);
        fallbackMaterial.diffuseColor = new BABYLON.Color3(1, 0, 0); // RED to indicate error
        this.stringerMesh.material = fallbackMaterial;

        // Position the stringer
        this.stringerMesh.position.x = 0;
        this.stringerMesh.position.y = totalRise / 2;
        this.stringerMesh.position.z = 0;

        console.log("Fallback stringer created with dimensions:", totalRun, "x", totalRise + thickness, "x", thickness);

        this.updateCamera();
    }

    applyStringerMaterial() {
        const material = new BABYLON.StandardMaterial("stringerMaterial", this.scene);
        material.diffuseColor = new BABYLON.Color3(0.8, 0.6, 0.4); // Wood color
        material.specularColor = new BABYLON.Color3(0.2, 0.2, 0.2);
        this.stringerMesh.material = material;
    }

    positionStringer() {
        const { unitRun, unitRise, numSteps, stringerLength } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Center the stringer properly
        this.stringerMesh.position.x = -totalRun / 2;
        this.stringerMesh.position.y = 0;
        this.stringerMesh.position.z = 0;

        // Rotate to see it better (optional)
        this.stringerMesh.rotation.x = 0;
        this.stringerMesh.rotation.y = 0;
        this.stringerMesh.rotation.z = 0;

        console.log("Stringer positioned at:", this.stringerMesh.position);
        console.log("Stringer dimensions - totalRun:", totalRun, "totalRise:", totalRise);

        this.updateCamera();
    }

    updateCamera() {
        const { unitRun, unitRise, numSteps, stringerLength } = this.parameters;
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;

        // Position camera to see the stringer well
        this.camera.setTarget(new BABYLON.Vector3(0, totalRise / 2, 0));
        this.camera.radius = Math.max(totalRun, totalRise) * 2;
        this.camera.alpha = -Math.PI / 4; // Angle around Y axis
        this.camera.beta = Math.PI / 3;   // Angle from Y axis

        console.log("Camera updated - target:", this.camera.target, "radius:", this.camera.radius);
    }
}

// Initialize the application when page loads
window.addEventListener('DOMContentLoaded', () => {
    new StringerGenerator();
});
