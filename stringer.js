class StringerGenerator {
    constructor() {
        this.canvas = document.getElementById('renderCanvas');
        this.engine = new BABYLON.Engine(this.canvas, true);
        this.scene = null;
        this.camera = null;
        this.stringerMesh = null;

        this.parameters = {
            unitRun: 280,
            unitRise: 180,
            thickness: 40,
            numSteps: 8,
            stringerLength: 3000
        };

        this.init();
        this.setupEventListeners();
        this.generateStringer();
    }

    init() {
        // Create scene
        this.scene = new BABYLON.Scene(this.engine);
        this.scene.clearColor = new BABYLON.Color3(0.1, 0.1, 0.15);

        // Create camera
        this.camera = new BABYLON.ArcRotateCamera(
            "camera",
            -Math.PI / 2,
            Math.PI / 3,
            2000,
            new BABYLON.Vector3(0, 0, 0),
            this.scene
        );
        this.camera.attachControls(this.canvas, true);
        this.camera.setTarget(BABYLON.Vector3.Zero());

        // Create lights
        const hemisphericLight = new BABYLON.HemisphericLight(
            "hemisphericLight",
            new BABYLON.Vector3(0, 1, 0),
            this.scene
        );
        hemisphericLight.intensity = 0.7;

        const directionalLight = new BABYLON.DirectionalLight(
            "directionalLight",
            new BABYLON.Vector3(-1, -1, -1),
            this.scene
        );
        directionalLight.intensity = 0.5;

        // Start render loop
        this.engine.runRenderLoop(() => {
            this.scene.render();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            this.engine.resize();
        });
    }

    setupEventListeners() {
        // Parameter controls
        const controls = ['unitRun', 'unitRise', 'thickness', 'numSteps', 'stringerLength'];

        controls.forEach(control => {
            const slider = document.getElementById(control);
            const valueDisplay = document.getElementById(control + 'Value');

            slider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                this.parameters[control] = value;
                valueDisplay.textContent = value;
                this.updateCalculations();
                this.generateStringer();
            });
        });

        // Regenerate button
        document.getElementById('regenerate').addEventListener('click', () => {
            this.generateStringer();
        });

        // Initialize displays
        controls.forEach(control => {
            const valueDisplay = document.getElementById(control + 'Value');
            valueDisplay.textContent = this.parameters[control];
        });

        this.updateCalculations();
    }

    updateCalculations() {
        const totalRise = this.parameters.unitRise * this.parameters.numSteps;
        const totalRun = this.parameters.unitRun * this.parameters.numSteps;
        const angle = Math.atan(totalRise / totalRun) * (180 / Math.PI);

        document.getElementById('totalRise').textContent = totalRise;
        document.getElementById('totalRun').textContent = totalRun;
        document.getElementById('angle').textContent = angle.toFixed(1);
    }

    createStringerProfile() {
        const { unitRun, unitRise, numSteps, stringerLength, thickness } = this.parameters;

        // Create the main stringer profile points with linear bottom edge
        const points = [];

        // Start at bottom left (0,0)
        points.push(new BABYLON.Vector3(0, 0, 0));

        // Linear bottom edge - VERY IMPORTANT requirement
        points.push(new BABYLON.Vector3(stringerLength, 0, 0));

        // Top right corner - calculate height based on stringer length and step geometry
        const totalRun = unitRun * numSteps;
        const totalRise = unitRise * numSteps;
        const angle = Math.atan(totalRise / totalRun);
        const topHeight = Math.tan(angle) * stringerLength + thickness;

        points.push(new BABYLON.Vector3(stringerLength, topHeight, 0));

        // Create stepped profile from right to left
        for (let i = numSteps - 1; i >= 0; i--) {
            const stepX = i * unitRun;
            const stepY = i * unitRise;

            // Top horizontal line of step (tread level + thickness)
            points.push(new BABYLON.Vector3(stepX + unitRun, stepY + unitRise + thickness, 0));

            // Vertical drop for riser cut
            points.push(new BABYLON.Vector3(stepX + unitRun, stepY + thickness, 0));

            // Horizontal line for tread cut
            points.push(new BABYLON.Vector3(stepX, stepY + thickness, 0));
        }

        // Left edge back to start
        points.push(new BABYLON.Vector3(0, thickness, 0));
        points.push(new BABYLON.Vector3(0, 0, 0));

        return points;
    }

    generateStringer() {
        // Remove existing stringer
        if (this.stringerMesh) {
            this.stringerMesh.dispose();
        }

        // Create profile points
        const profilePoints = this.createStringerProfile();

        // Create 2D shape from points
        const shape = profilePoints.map(point => new BABYLON.Vector3(point.x, point.y, 0));

        // Extrude the shape to create 3D stringer
        const extrudeOptions = {
            shape: shape,
            depth: this.parameters.thickness,
            sideOrientation: BABYLON.Mesh.DOUBLESIDE
        };

        this.stringerMesh = BABYLON.MeshBuilder.ExtrudePolygon("stringer", extrudeOptions, this.scene);

        // Create material
        const material = new BABYLON.StandardMaterial("stringerMaterial", this.scene);
        material.diffuseColor = new BABYLON.Color3(0.8, 0.6, 0.4); // Wood color
        material.specularColor = new BABYLON.Color3(0.2, 0.2, 0.2);
        material.roughness = 0.8;

        this.stringerMesh.material = material;

        // Position the stringer
        this.stringerMesh.position.x = -this.parameters.stringerLength / 2;
        this.stringerMesh.position.y = -this.parameters.thickness / 2;
        this.stringerMesh.rotation.z = 0;

        // Update camera target
        this.camera.setTarget(new BABYLON.Vector3(0, this.parameters.unitRise * this.parameters.numSteps / 2, 0));
    }
}

// Initialize the application when page loads
window.addEventListener('DOMContentLoaded', () => {
    new StringerGenerator();
});
